/**
 * Test file for distance parsing improvements
 * Tests the parseDistanceValue function logic
 */

// Helper function to parse distance with improved validation and comma handling
const parseDistanceValue = (distanceString) => {
  if (!distanceString || typeof distanceString !== 'string') {
    return 0;
  }
  
  // Remove commas and extract numeric value with improved regex
  // This handles formats like: "1,234.5 km", "1.5 km", "500 m", "1,000 m"
  const cleanDistance = distanceString.replace(/,/g, '');
  const distanceMatch = cleanDistance.match(/(\d+(?:\.\d+)?)/);
  
  if (!distanceMatch) {
    return 0;
  }
  
  const numericValue = parseFloat(distanceMatch[1]);
  
  // Convert meters to kilometers if needed
  if (cleanDistance.toLowerCase().includes('m') && !cleanDistance.toLowerCase().includes('km')) {
    return numericValue / 1000;
  }
  
  return numericValue;
};

// Test cases
const testCases = [
  // Standard formats
  { input: "1.5 km", expected: 1.5, description: "Standard km format" },
  { input: "500 m", expected: 0.5, description: "Meters to km conversion" },
  { input: "2.3 km", expected: 2.3, description: "Decimal km format" },
  
  // Comma-separated numbers
  { input: "1,234.5 km", expected: 1234.5, description: "Comma-separated km" },
  { input: "1,000 m", expected: 1.0, description: "Comma-separated meters" },
  { input: "12,345 m", expected: 12.345, description: "Large comma-separated meters" },
  
  // Edge cases
  { input: "", expected: 0, description: "Empty string" },
  { input: null, expected: 0, description: "Null input" },
  { input: undefined, expected: 0, description: "Undefined input" },
  { input: "invalid", expected: 0, description: "Invalid format" },
  { input: "km", expected: 0, description: "No numeric value" },
  
  // Real-world examples
  { input: "15.7 km", expected: 15.7, description: "Real routing distance" },
  { input: "850 m", expected: 0.85, description: "Short distance in meters" },
  { input: "2,150.3 km", expected: 2150.3, description: "Long distance with comma" }
];

// Run tests
console.log("🧪 Testing distance parsing improvements...\n");

let passed = 0;
let failed = 0;

testCases.forEach((testCase, index) => {
  const result = parseDistanceValue(testCase.input);
  const success = Math.abs(result - testCase.expected) < 0.001; // Allow for floating point precision
  
  if (success) {
    console.log(`✅ Test ${index + 1}: ${testCase.description}`);
    console.log(`   Input: "${testCase.input}" → Output: ${result} (Expected: ${testCase.expected})`);
    passed++;
  } else {
    console.log(`❌ Test ${index + 1}: ${testCase.description}`);
    console.log(`   Input: "${testCase.input}" → Output: ${result} (Expected: ${testCase.expected})`);
    failed++;
  }
  console.log();
});

console.log(`📊 Test Results: ${passed} passed, ${failed} failed`);

if (failed === 0) {
  console.log("🎉 All tests passed! Distance parsing improvements are working correctly.");
} else {
  console.log("⚠️  Some tests failed. Please review the implementation.");
}
