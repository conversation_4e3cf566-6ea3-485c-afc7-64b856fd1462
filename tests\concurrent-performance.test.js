/**
 * Performance test to demonstrate the improvement from concurrent execution
 * This simulates the difference between sequential and concurrent distance calculations
 */

// Mock async function that simulates a routing API call
const mockRoutingCall = async (providerId, delay = 100) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        providerId,
        distance: `${(Math.random() * 10 + 1).toFixed(1)} km`,
        duration: `${Math.floor(Math.random() * 20 + 5)} min`
      });
    }, delay);
  });
};

// Sequential execution (old approach)
const processProvidersSequentially = async (providers) => {
  const startTime = Date.now();
  const results = [];
  
  for (const provider of providers) {
    try {
      const result = await mockRoutingCall(provider.id, 100); // 100ms delay per call
      results.push({ ...provider, ...result });
    } catch (error) {
      results.push({ ...provider, distance: 'Error', duration: 'N/A' });
    }
  }
  
  const endTime = Date.now();
  return {
    results,
    duration: endTime - startTime,
    method: 'Sequential'
  };
};

// Concurrent execution with Promise.all (current approach)
const processProvidersConcurrentlyAll = async (providers) => {
  const startTime = Date.now();
  
  const operations = providers.map(async (provider) => {
    try {
      const result = await mockRoutingCall(provider.id, 100); // 100ms delay per call
      return { ...provider, ...result };
    } catch (error) {
      return { ...provider, distance: 'Error', duration: 'N/A' };
    }
  });
  
  const results = await Promise.all(operations);
  const endTime = Date.now();
  
  return {
    results,
    duration: endTime - startTime,
    method: 'Promise.all'
  };
};

// Concurrent execution with Promise.allSettled (improved approach)
const processProvidersConcurrentlyAllSettled = async (providers) => {
  const startTime = Date.now();
  
  const operations = providers.map(async (provider) => {
    const result = await mockRoutingCall(provider.id, 100); // 100ms delay per call
    return { ...provider, ...result };
  });
  
  const results = await Promise.allSettled(operations);
  const processedResults = results.map((result, index) => {
    if (result.status === 'fulfilled') {
      return result.value;
    } else {
      return { 
        ...providers[index], 
        distance: 'Error', 
        duration: 'N/A',
        error: result.reason?.message || 'Unknown error'
      };
    }
  });
  
  const endTime = Date.now();
  
  return {
    results: processedResults,
    duration: endTime - startTime,
    method: 'Promise.allSettled'
  };
};

// Test with sample providers
const sampleProviders = [
  { id: 1, name: 'Provider A', address: 'Balanga City' },
  { id: 2, name: 'Provider B', address: 'Dinalupihan' },
  { id: 3, name: 'Provider C', address: 'Hermosa' },
  { id: 4, name: 'Provider D', address: 'Orani' },
  { id: 5, name: 'Provider E', address: 'Samal' },
  { id: 6, name: 'Provider F', address: 'Abucay' },
  { id: 7, name: 'Provider G', address: 'Pilar' },
  { id: 8, name: 'Provider H', address: 'Orion' }
];

// Run performance comparison
const runPerformanceTest = async () => {
  console.log('🚀 Running Performance Comparison Test\n');
  console.log(`Testing with ${sampleProviders.length} providers...\n`);
  
  // Test sequential execution
  console.log('⏳ Testing Sequential Execution...');
  const sequentialResult = await processProvidersSequentially(sampleProviders);
  console.log(`✅ Sequential: ${sequentialResult.duration}ms\n`);
  
  // Test Promise.all execution
  console.log('⏳ Testing Promise.all Execution...');
  const promiseAllResult = await processProvidersConcurrentlyAll(sampleProviders);
  console.log(`✅ Promise.all: ${promiseAllResult.duration}ms\n`);
  
  // Test Promise.allSettled execution
  console.log('⏳ Testing Promise.allSettled Execution...');
  const promiseAllSettledResult = await processProvidersConcurrentlyAllSettled(sampleProviders);
  console.log(`✅ Promise.allSettled: ${promiseAllSettledResult.duration}ms\n`);
  
  // Calculate improvements
  const allImprovement = ((sequentialResult.duration - promiseAllResult.duration) / sequentialResult.duration * 100).toFixed(1);
  const allSettledImprovement = ((sequentialResult.duration - promiseAllSettledResult.duration) / sequentialResult.duration * 100).toFixed(1);
  
  console.log('📊 Performance Summary:');
  console.log('='.repeat(50));
  console.log(`Sequential:        ${sequentialResult.duration}ms`);
  console.log(`Promise.all:       ${promiseAllResult.duration}ms (${allImprovement}% faster)`);
  console.log(`Promise.allSettled: ${promiseAllSettledResult.duration}ms (${allSettledImprovement}% faster)`);
  console.log('='.repeat(50));
  
  console.log('\n🎯 Key Benefits of Concurrent Execution:');
  console.log('• Significantly reduced response time');
  console.log('• Better user experience with faster loading');
  console.log('• More efficient resource utilization');
  console.log('• Promise.allSettled provides better error resilience');
  
  return {
    sequential: sequentialResult.duration,
    promiseAll: promiseAllResult.duration,
    promiseAllSettled: promiseAllSettledResult.duration,
    improvement: allSettledImprovement
  };
};

// Run the test
runPerformanceTest().catch(console.error);
