/**
 * Test to demonstrate routing performance improvements with timeout and caching
 */

// Mock routing service that simulates different scenarios
class MockRoutingService {
  constructor() {
    this.cache = new Map();
    this.callCount = 0;
  }

  async getRoute(start, end, options = {}) {
    this.callCount++;
    const timeout = options.timeout || 5000;
    const key = `${start[0]},${start[1]}-${end[0]},${end[1]}`;
    
    // Check cache first
    if (this.cache.has(key)) {
      console.log(`📍 [Cache Hit] Route ${key}`);
      return this.cache.get(key);
    }

    // Simulate different response times and potential failures
    const scenarios = [
      { delay: 100, success: true, name: 'Fast API' },
      { delay: 2000, success: true, name: 'Slow API' },
      { delay: 6000, success: true, name: 'Very Slow API' },
      { delay: 1000, success: false, name: 'Failed API' }
    ];

    const scenario = scenarios[Math.floor(Math.random() * scenarios.length)];
    
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        if (scenario.success) {
          const result = {
            distance: `${(Math.random() * 10 + 1).toFixed(1)} km`,
            duration: `${Math.floor(Math.random() * 20 + 5)} min`,
            provider: scenario.name,
            trafficAware: false
          };
          
          // Cache the result
          this.cache.set(key, result);
          console.log(`📍 [API Success] ${scenario.name} responded in ${scenario.delay}ms`);
          resolve(result);
        } else {
          console.log(`📍 [API Failure] ${scenario.name} failed after ${scenario.delay}ms`);
          reject(new Error(`${scenario.name} failed`));
        }
      }, scenario.delay);

      // Implement timeout
      const timeoutTimer = setTimeout(() => {
        clearTimeout(timer);
        console.log(`📍 [Timeout] Request timed out after ${timeout}ms`);
        reject(new Error(`Request timed out after ${timeout}ms`));
      }, timeout);

      // Clear timeout timer when request completes
      const originalResolve = resolve;
      const originalReject = reject;
      
      resolve = (value) => {
        clearTimeout(timeoutTimer);
        originalResolve(value);
      };
      
      reject = (error) => {
        clearTimeout(timeoutTimer);
        originalReject(error);
      };
    });
  }

  getStats() {
    return {
      totalCalls: this.callCount,
      cacheSize: this.cache.size,
      cacheHitRate: this.cache.size > 0 ? (this.cache.size / this.callCount * 100).toFixed(1) : 0
    };
  }
}

// Test function without timeout (old approach)
const testWithoutTimeout = async (routingService, routes) => {
  console.log('\n🔴 Testing WITHOUT timeout protection...');
  const startTime = Date.now();
  const results = [];
  
  for (const route of routes) {
    try {
      const result = await routingService.getRoute(route.start, route.end);
      results.push({ ...route, success: true, result });
    } catch (error) {
      results.push({ ...route, success: false, error: error.message });
    }
  }
  
  const endTime = Date.now();
  const successCount = results.filter(r => r.success).length;
  
  return {
    duration: endTime - startTime,
    successCount,
    totalCount: routes.length,
    successRate: (successCount / routes.length * 100).toFixed(1)
  };
};

// Test function with timeout (new approach)
const testWithTimeout = async (routingService, routes, timeout = 5000) => {
  console.log(`\n🟢 Testing WITH ${timeout}ms timeout protection...`);
  const startTime = Date.now();
  const results = [];
  
  for (const route of routes) {
    try {
      const result = await routingService.getRoute(route.start, route.end, { timeout });
      results.push({ ...route, success: true, result });
    } catch (error) {
      results.push({ ...route, success: false, error: error.message });
    }
  }
  
  const endTime = Date.now();
  const successCount = results.filter(r => r.success).length;
  
  return {
    duration: endTime - startTime,
    successCount,
    totalCount: routes.length,
    successRate: (successCount / routes.length * 100).toFixed(1)
  };
};

// Test concurrent processing with timeout
const testConcurrentWithTimeout = async (routingService, routes, timeout = 5000) => {
  console.log(`\n🚀 Testing CONCURRENT processing with ${timeout}ms timeout...`);
  const startTime = Date.now();
  
  const routePromises = routes.map(async (route) => {
    try {
      const result = await routingService.getRoute(route.start, route.end, { timeout });
      return { ...route, success: true, result };
    } catch (error) {
      return { ...route, success: false, error: error.message };
    }
  });
  
  const results = await Promise.allSettled(routePromises);
  const processedResults = results.map(r => r.status === 'fulfilled' ? r.value : r.reason);
  
  const endTime = Date.now();
  const successCount = processedResults.filter(r => r.success).length;
  
  return {
    duration: endTime - startTime,
    successCount,
    totalCount: routes.length,
    successRate: (successCount / routes.length * 100).toFixed(1)
  };
};

// Run the performance comparison
const runPerformanceTest = async () => {
  console.log('🧪 Testing Routing Performance Improvements\n');
  
  // Sample routes for testing
  const testRoutes = [
    { start: [14.6761, 120.5439], end: [14.8775, 120.4667], name: 'Balanga to Dinalupihan' },
    { start: [14.6761, 120.5439], end: [14.8333, 120.5000], name: 'Balanga to Hermosa' },
    { start: [14.6761, 120.5439], end: [14.8004, 120.5292], name: 'Balanga to Orani' },
    { start: [14.6761, 120.5439], end: [14.7667, 120.5167], name: 'Balanga to Samal' },
    { start: [14.6761, 120.5439], end: [14.7333, 120.5333], name: 'Balanga to Abucay' },
    { start: [14.6761, 120.5439], end: [14.6667, 120.5667], name: 'Balanga to Pilar' }
  ];

  console.log(`Testing with ${testRoutes.length} routes...\n`);

  // Test 1: Without timeout (old approach)
  const routingService1 = new MockRoutingService();
  const result1 = await testWithoutTimeout(routingService1, testRoutes);
  
  // Test 2: With timeout (new approach)
  const routingService2 = new MockRoutingService();
  const result2 = await testWithTimeout(routingService2, testRoutes, 5000);
  
  // Test 3: Concurrent with timeout (best approach)
  const routingService3 = new MockRoutingService();
  const result3 = await testConcurrentWithTimeout(routingService3, testRoutes, 5000);

  // Display results
  console.log('\n📊 Performance Comparison Results:');
  console.log('='.repeat(60));
  console.log(`Without Timeout:     ${result1.duration}ms | Success: ${result1.successCount}/${result1.totalCount} (${result1.successRate}%)`);
  console.log(`With Timeout:        ${result2.duration}ms | Success: ${result2.successCount}/${result2.totalCount} (${result2.successRate}%)`);
  console.log(`Concurrent+Timeout:  ${result3.duration}ms | Success: ${result3.successCount}/${result3.totalCount} (${result3.successRate}%)`);
  console.log('='.repeat(60));

  // Calculate improvements
  const timeoutImprovement = ((result1.duration - result2.duration) / result1.duration * 100).toFixed(1);
  const concurrentImprovement = ((result1.duration - result3.duration) / result1.duration * 100).toFixed(1);

  console.log('\n🎯 Key Improvements:');
  console.log(`• Timeout protection: ${timeoutImprovement}% faster, prevents hanging requests`);
  console.log(`• Concurrent processing: ${concurrentImprovement}% faster overall`);
  console.log('• Server-side caching: Eliminates repeated API calls');
  console.log('• Graceful fallbacks: Maintains functionality when APIs fail');
  console.log('• Better user experience: Faster response times and reliability');

  // Show caching stats
  console.log('\n📈 Caching Statistics:');
  [routingService1, routingService2, routingService3].forEach((service, index) => {
    const stats = service.getStats();
    console.log(`Service ${index + 1}: ${stats.totalCalls} calls, ${stats.cacheSize} cached, ${stats.cacheHitRate}% hit rate`);
  });
};

// Run the test
runPerformanceTest().catch(console.error);
